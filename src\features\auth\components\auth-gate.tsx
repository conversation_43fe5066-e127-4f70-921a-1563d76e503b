"use client";

import { useAuthStatus } from "@/features/auth/hooks/use-auth-status.hook";
import { ReactNode } from "react";

interface AuthGateProps {
	children: ReactNode;
	fallback?: ReactNode;
}

export function AuthGate({ children, fallback }: AuthGateProps) {
	const { isAuthenticated, isLoading } = useAuthStatus();

	if (isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			</div>
		);
	}

	if (isAuthenticated === false) {
		return (
			fallback || (
				<div className="min-h-screen flex items-center justify-center">
					<div className="text-center">
						<h2 className="text-xl font-semibold text-gray-800">Redirecionando para login...</h2>
					</div>
				</div>
			)
		);
	}

	return <>{children}</>;
}
