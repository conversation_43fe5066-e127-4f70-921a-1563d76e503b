// Singleton para gerenciar o estado do Keycloak
class KeycloakManager {
	private static instance: KeycloakManager;
	private isInitializing = false;
	private isInitialized = false;
	private isLoggingOut = false;
	private initPromise: Promise<boolean> | null = null;

	public static getInstance(): KeycloakManager {
		if (!KeycloakManager.instance) {
			KeycloakManager.instance = new KeycloakManager();
		}
		return KeycloakManager.instance;
	}

	public isKeycloakInitialized(): boolean {
		return this.isInitialized;
	}

	public isKeycloakInitializing(): boolean {
		return this.isInitializing;
	}

	public isKeycloakLoggingOut(): boolean {
		return this.isLoggingOut;
	}

	public getInitPromise(): Promise<boolean> | null {
		return this.initPromise;
	}

	public setInitializing(value: boolean): void {
		this.isInitializing = value;
	}

	public setInitialized(value: boolean): void {
		this.isInitialized = value;
	}

	public setLoggingOut(value: boolean): void {
		this.isLoggingOut = value;
	}

	public setInitPromise(promise: Promise<boolean> | null): void {
		this.initPromise = promise;
	}

	public reset(): void {
		this.isInitialized = false;
		this.isInitializing = false;
		this.isLoggingOut = false;
		this.initPromise = null;
	}
}

export default KeycloakManager;
