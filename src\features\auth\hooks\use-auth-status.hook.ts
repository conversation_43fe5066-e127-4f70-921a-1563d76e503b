"use client";

import { useState, useEffect } from "react";
import { getCookie } from "@/shared/lib/cookies/crud/get";
import { COOKIE_NAMES } from "@/config/cookies/name";
import keycloak from "@/config/keycloack/config";

export function useAuthStatus() {
	const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const checkAuthStatus = async () => {
			try {
				// Primeiro verifica o cookie
				const tokenResult = await getCookie({ name: COOKIE_NAMES.ACCESS_TOKEN });
				const hasToken = tokenResult.success && tokenResult.value;

				// Se tem token, considera autenticado
				if (hasToken) {
					setIsAuthenticated(true);
					setIsLoading(false);
					return;
				}

				// Se não tem token, verifica o Keycloak
				if (keycloak.authenticated !== undefined) {
					setIsAuthenticated(keycloak.authenticated);
				} else {
					setIsAuthenticated(false);
				}

				setIsLoading(false);
			} catch (error) {
				console.error("Erro ao verificar status de autenticação:", error);
				setIsAuthenticated(false);
				setIsLoading(false);
			}
		};

		checkAuthStatus();
	}, []);

	return { isAuthenticated, isLoading };
}
