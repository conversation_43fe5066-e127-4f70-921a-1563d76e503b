"use client";

import keycloak from "@/config/keycloack/config";
import { removeCookie } from "@/shared/lib/cookies/crud/remove";
import { COOKIE_NAMES } from "@/config/cookies/name";
import KeycloakManager from "../services/keycloak-manager";

export const performLogout = async (): Promise<void> => {
	const manager = KeycloakManager.getInstance();

	try {
		console.log("Iniciando processo de logout...");

		// Marca que está fazendo logout
		manager.setLoggingOut(true);

		// Remove o token dos cookies primeiro
		await removeCookie({ name: COOKIE_NAMES.ACCESS_TOKEN });
		console.log("Token removido dos cookies");

		// Limpa o estado do Keycloak
		keycloak.authenticated = false;
		keycloak.token = undefined;

		// Reset do manager
		manager.reset();

		// Faz logout no Keycloak se estiver disponível
		if (keycloak && keycloak.logout) {
			console.log("Fazendo logout no Keycloak...");
			console.log(window.location.origin);
			await keycloak.logout({
				redirectUri: "http://localhost:3000/",
			});
		} else {
			console.log("Keycloak não disponível, redirecionando para login...");
			// window.location.href = "/login";
		}
	} catch (error) {
		console.error("Erro durante logout:", error);

		// Em caso de erro, força a limpeza e redireciona
		manager.reset();
		keycloak.authenticated = false;
		keycloak.token = undefined;

		// // Fallback: redireciona para login
		// window.location.href = "/login";
	}
};
