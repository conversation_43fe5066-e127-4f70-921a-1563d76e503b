import Keycloak from "keycloak-js";

const keycloak = new Keycloak({
	url: "http://192.168.155.83:8080",
	realm: "master",
	clientId: "simp",
});

// Configurações adicionais para melhorar a experiência do usuário
keycloak.onAuthSuccess = () => {
	console.log("Auth success");
};

keycloak.onAuthError = error => {
	console.error("Auth error:", error);
};

keycloak.onTokenExpired = () => {
	console.log("Token expired, attempting refresh");
	keycloak.updateToken(30).catch(() => {
		console.log("Failed to refresh token, redirecting to login");
	});
};

export default keycloak;
